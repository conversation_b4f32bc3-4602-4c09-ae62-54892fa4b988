package com.cairh.cpe.monitor.backend.controller;

import com.cairh.cpe.common.dto.*;
import com.cairh.cpe.common.service.IAuditReportService;
import com.cairh.cpe.context.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 见证大屏数据
 */
@Slf4j
@RestController
public class AuditReportController {

    @Resource
    private IAuditReportService auditReportService;

    /**
     * 见证人在线概况（总部）
     */
    @RequestMapping(value = {"/monitor/AC241001", "/authless/monitor/AC241001"}, method = RequestMethod.POST)
    public Result<AuditOnlineDetailResp> queryAC241001() {
        return Result.success(auditReportService.queryAC241001());
    }

    /**
     * 见证人承压概况（总部）
     */
    @RequestMapping(value = {"/monitor/AC241002", "/authless/monitor/AC241002"}, method = RequestMethod.POST)
    public Result<List<AuditPressureDetailResp>> queryAC241002() {
        return Result.success(auditReportService.queryAC241002());
    }

    /**
     * 见证人产能分布（总部）
     */
    @RequestMapping(value = {"/monitor/AC241003", "/authless/monitor/AC241003"}, method = RequestMethod.POST)
    public Result<AuditCapacityDetailResp> queryAC241003() {
        return Result.success(auditReportService.queryAC241003());
    }

    /**
     * 绿通客户服务概况
     */
    @RequestMapping(value = {"/monitor/AC241004", "/authless/monitor/AC241004"}, method = RequestMethod.POST)
    public Result<AuditWhiteDetailResp> queryAC241004() {
        return Result.success(auditReportService.queryAC241004());
    }

    /**
     * 告警监控大屏详情
     */
    @RequestMapping(value = {"/monitor/AC242001", "/authless/monitor/AC242001"}, method = RequestMethod.POST)
    public Result<AuditAlarmDetailResp> queryAC242001() {
        return Result.success(auditReportService.queryAC242001());
    }

}
