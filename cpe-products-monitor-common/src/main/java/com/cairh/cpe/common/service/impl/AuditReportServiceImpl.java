package com.cairh.cpe.common.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.cairh.cpe.common.constant.CacheKeyConfig;
import com.cairh.cpe.common.constant.CapacityTypeEnum;
import com.cairh.cpe.common.constant.Constant;
import com.cairh.cpe.common.constant.PressureTypeEnum;
import com.cairh.cpe.common.dto.*;
import com.cairh.cpe.common.entity.*;
import com.cairh.cpe.common.mapper.AuditReportMapper;
import com.cairh.cpe.common.service.IAuditAlarmService;
import com.cairh.cpe.common.service.IAuditReportService;
import com.cairh.cpe.common.util.RedisService;
import com.cairh.cpe.common.util.SysParamService;
import com.cairh.cpe.context.BizException;
import com.cairh.cpe.core.autoconfiure.env.CompositePropertySources;
import com.cairh.cpe.esb.base.rpc.IVBaseOnlineUserDubboService;
import com.cairh.cpe.esb.base.rpc.IVBaseUserInfoDubboService;
import com.cairh.cpe.esb.base.rpc.dto.req.VBaseQryOperatorinfoRequest;
import com.cairh.cpe.esb.base.rpc.dto.resp.OnlineUserResponse;
import com.cairh.cpe.esb.base.rpc.dto.resp.VBaseQryOperatorinfoResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.apache.dubbo.common.utils.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;


@Slf4j
@Service
public class AuditReportServiceImpl implements IAuditReportService {
    private static final String ALL_DATA = "总览";

    @Resource
    protected CompositePropertySources compositePropertySources;
    @Autowired
    private RedisService redisService;
    @DubboReference(check = false, lazy = true)
    private IVBaseOnlineUserDubboService baseOnlineUserDubboService;
    @DubboReference(check = false, lazy = true)
    private IVBaseUserInfoDubboService baseUserInfoDubboService;
    @Resource
    private AuditReportMapper auditReportMapper;
    @Resource
    private SysParamService sysParamService;
    @Autowired
    @Qualifier("monitorExecutor")
    private ThreadPoolTaskExecutor monitorExecutor;
    @Resource
    private IAuditAlarmService auditAlarmService;

    private String getRate(int v1, int v2) {
        if (v1 == 0 || v2 == 0) {
            return "0";
        }
        return BigDecimal.valueOf(v1)
                .divide(BigDecimal.valueOf(v2), 12, RoundingMode.HALF_UP)
                .multiply(BigDecimal.valueOf(100))
                .setScale(0, RoundingMode.DOWN).toString();
    }

    @Override
    public AuditOnlineDetailResp queryAC241001() {
        AuditOnlineDetailResp auditOnlineDetailResp = new AuditOnlineDetailResp();
        // 获取系统参数配置
        String groupConfig = compositePropertySources.getProperty(Constant.MONITOR_AC_OPERATOR_GROUP);
        if (StringUtils.isBlank(groupConfig)) {
            log.warn("[queryAC241001] 获取系统参数配置={}为空", Constant.MONITOR_AC_OPERATOR_GROUP);
            return auditOnlineDetailResp;
        }
        try {
            // 获取在线操作员
            List<String> onlineUserList = baseOnlineUserDubboService.onlineUserInfo().stream().map(OnlineUserResponse::getStaff_no).collect(Collectors.toList());
            // 获取可接收派单任务的操作员
            List<String> workingUserList = redisService.zSetGetAllValue(CacheKeyConfig.DISPATCH_WORKING_OPERATOR_QUEUE);
            List<OperatorGroupVo> groupList = JSONArray.parseArray(groupConfig).toJavaList(OperatorGroupVo.class);
            // 所有操作员，去重
            List<String> operatorNos = groupList.stream()
                    .map(group -> Arrays.asList(group.getGroup_operators().split(Constant.COMMA)))
                    .flatMap(List::stream)
                    .distinct()
                    .collect(Collectors.toList());
            log.info("[queryAC241001] 获取所有操作员={}", JSON.toJSONString(operatorNos));
            // 配置组的操作员并且在线的操作员
            List<String> onlineOperatorNos = operatorNos.stream().filter(onlineUserList::contains).collect(Collectors.toList());
            // 配置组的操作员并且在派单中的操作员
            List<String> workingOperatorNos = operatorNos.stream().filter(workingUserList::contains).collect(Collectors.toList());
            log.info("[queryAC241001]配置组的操作员并且在派单中的操作员={}", JSON.toJSONString(workingOperatorNos));
            // 查询配置操作员信息
            VBaseQryOperatorinfoRequest vBaseQryOperatorinfoRequest = new VBaseQryOperatorinfoRequest();
            vBaseQryOperatorinfoRequest.setStaff_no_list(operatorNos);
            List<VBaseQryOperatorinfoResponse> operatorList = baseUserInfoDubboService.baseUserQryOperatorinfo(vBaseQryOperatorinfoRequest);
            // 将list转为根据操作员编号做map处理
            Map<String, VBaseQryOperatorinfoResponse> operatorMap = operatorList
                    .stream()
                    .collect(Collectors.toMap(VBaseQryOperatorinfoResponse::getStaff_no, Function.identity()));

            // 根据配置操作员编号构建操作员详情列表
            List<AuditGroupDetail> auditGroupDetailList = groupList.stream().map(group -> {
                AuditGroupDetail auditGroupDetail = new AuditGroupDetail();
                auditGroupDetail.setGroup_name(group.getGroup_name());
                auditGroupDetail.setGroup_area(group.getGroup_area());
                List<String> nos = Arrays.asList(group.getGroup_operators().split(Constant.COMMA));
                List<AuditGroupDetail.AuditUserInfo> auditUserInfoList = nos.stream().map(operatorNo -> {
                    AuditGroupDetail.AuditUserInfo auditUserInfo = new AuditGroupDetail.AuditUserInfo();
                    auditUserInfo.setOperator_no(operatorNo);
                    auditUserInfo.setOperator_name(Objects.nonNull(operatorMap.get(operatorNo)) ? operatorMap.get(operatorNo).getUser_name() : operatorNo);
                    if (onlineUserList.contains(operatorNo)) {
                        auditUserInfo.setStatus(Constant.USER_STATUS_1);
                    }
                    if (workingUserList.contains(operatorNo)) {
                        auditUserInfo.setStatus(Constant.USER_STATUS_2);
                    }
                    return auditUserInfo;
                }).sorted(Comparator.comparing(AuditGroupDetail.AuditUserInfo::getStatus).reversed()).collect(Collectors.toList());
                auditGroupDetail.setAuditUserInfoList(auditUserInfoList);
                return auditGroupDetail;
            }).collect(Collectors.toList());
            String onlineRate = getRate(onlineOperatorNos.size(), operatorNos.size());
            auditOnlineDetailResp.setOnline_num(onlineOperatorNos.size());
            auditOnlineDetailResp.setOnline_rate(onlineRate);
            auditOnlineDetailResp.setOffline_num(operatorNos.size() - onlineOperatorNos.size());
            auditOnlineDetailResp.setOffline_rate(String.valueOf(100 - Integer.parseInt(onlineRate)));
            auditOnlineDetailResp.setDispatch_num(workingOperatorNos.size());
            auditOnlineDetailResp.setDispatch_rate(getRate(workingOperatorNos.size(), onlineOperatorNos.size()));
            auditOnlineDetailResp.setAuditGroupDetailList(auditGroupDetailList);
        } catch (Exception e) {
            log.error("获取数据异常", e);
            throw new BizException("获取数据异常");
        }
        return auditOnlineDetailResp;
    }

    @Override
    public List<AuditPressureDetailResp> queryAC241002() {
        List<AuditPressureDetailResp> auditPressureDetailRespList = new ArrayList<>();
        // 获取配置参数中的操作员组信息
        List<OperatorGroupVo> groupList = getConfigOperator();
        // 获取当前在线的配置操作员列表
        List<String> onlineAcOperatorNolist = getConfigOnlineOperator(groupList);
        if (CollectionUtils.isEmpty(onlineAcOperatorNolist)) {
            log.info("[queryAC241002] 配置组的操作员，无在线操作员!");
            return auditPressureDetailRespList;
        }
        // 查询配置操作员信息
        List<AcOperatorInfo> acOperatorInfoList = auditReportMapper.getAcOperatorInfoList(onlineAcOperatorNolist);
        Map<String, AcOperatorInfo> headOperatorInfoMap = acOperatorInfoList
                .stream()
                .collect(Collectors.toMap(AcOperatorInfo::getStaff_no, Function.identity()));

        String unitTime = sysParamService.getMonitorAcStatisticsUnitTime();
        List<AcOperatorTaskCount> headOperatorTaskCountList = auditReportMapper.getAcOperatorTaskCount(unitTime, onlineAcOperatorNolist);
        // 构建操作员任务计数映射表，便于快速查询
        Map<String, AcOperatorTaskCount> taskCountMap = headOperatorTaskCountList.stream()
                .collect(Collectors.toMap(AcOperatorTaskCount::getStaff_no, Function.identity()));
        for (OperatorGroupVo operatorGroupVo : groupList) {
            // 承压人数
            AtomicInteger pressNum = new AtomicInteger();
            // 忙碌人数
            AtomicInteger busyNum = new AtomicInteger();
            // 正常人数
            AtomicInteger normalNum = new AtomicInteger();
            // 空闲人数
            AtomicInteger idleNum = new AtomicInteger();
            AuditPressureDetailResp auditPressureDetailResp = new AuditPressureDetailResp();
            auditPressureDetailResp.setArea_name(operatorGroupVo.getGroup_area());
            List<String> operatorList = Arrays.asList(operatorGroupVo.getGroup_operators().split(Constant.COMMA));
            // 根据总部操作员编号集合构建承压用户详情列表
            List<AuditPressureDetailResp.PressureUserDetail> pressureUserDetailList = operatorList.stream().map(operatorNo -> {
                AcOperatorTaskCount taskCount = taskCountMap.get(operatorNo);
                AuditPressureDetailResp.PressureUserDetail pressureUserDetail = new AuditPressureDetailResp.PressureUserDetail();
                pressureUserDetail.setOperator_no(operatorNo);
                if (Objects.isNull(taskCount)) {
                    // 如果当前操作员在线，则视为空闲状态，离线状态不做处理
                    if (onlineAcOperatorNolist.contains(operatorNo)) {
                        idleNum.getAndIncrement();
                        AcOperatorInfo acOperatorInfo = headOperatorInfoMap.get(operatorNo);
                        buildDetail(pressureUserDetail, operatorNo, acOperatorInfo);
                        pressureUserDetail.setPressure_type(PressureTypeEnum.IDLE.getCode());
                        pressureUserDetail.setNum(0);
                        return pressureUserDetail;
                    }
                    return null;
                }
                // 获取当前操作员的信息
                AcOperatorInfo acOperatorInfo = headOperatorInfoMap.get(operatorNo);
                buildDetail(pressureUserDetail, operatorNo, acOperatorInfo);
                pressureUserDetail.setNum(taskCount.getNum());

                if (taskCount.getNum() >= 10) {
                    pressureUserDetail.setPressure_type(PressureTypeEnum.PRESS.getCode());
                    pressNum.getAndIncrement();
                } else if (taskCount.getNum() >= 5) {
                    pressureUserDetail.setPressure_type(PressureTypeEnum.BUSY.getCode());
                    busyNum.getAndIncrement();
                } else if (taskCount.getNum() >= 1) {
                    pressureUserDetail.setPressure_type(PressureTypeEnum.NORMAL.getCode());
                    normalNum.getAndIncrement();
                }
                return pressureUserDetail;
            }).filter(Objects::nonNull).sorted(Comparator.comparing(AuditPressureDetailResp.PressureUserDetail::getNum).reversed()).collect(Collectors.toList());
            // 设置承压详情响应对象的各项数值
            auditPressureDetailResp.setPress_num(pressNum.get());
            auditPressureDetailResp.setBusy_num(busyNum.get());
            auditPressureDetailResp.setNormal_num(normalNum.get());
            auditPressureDetailResp.setIdle_num(idleNum.get());
            auditPressureDetailResp.setPressureUserDetailList(pressureUserDetailList);
            auditPressureDetailRespList.add(auditPressureDetailResp);
        }
        List<AuditPressureDetailResp> newAuditPressureDetailRespList = new ArrayList<>(auditPressureDetailRespList);
        // 所有数据合并为总览
        AuditPressureDetailResp auditPressureDetailResp = new AuditPressureDetailResp();
        auditPressureDetailResp.setArea_name(ALL_DATA);
        auditPressureDetailResp.setPress_num(auditPressureDetailRespList.stream().mapToInt(AuditPressureDetailResp::getPress_num).sum());
        auditPressureDetailResp.setBusy_num(auditPressureDetailRespList.stream().mapToInt(AuditPressureDetailResp::getBusy_num).sum());
        auditPressureDetailResp.setNormal_num(auditPressureDetailRespList.stream().mapToInt(AuditPressureDetailResp::getNormal_num).sum());
        auditPressureDetailResp.setIdle_num(auditPressureDetailRespList.stream().mapToInt(AuditPressureDetailResp::getIdle_num).sum());
        // 获取所有承压用户详情列表
        auditPressureDetailResp.setPressureUserDetailList(
                newAuditPressureDetailRespList.stream()
                        .map(AuditPressureDetailResp::getPressureUserDetailList)
                        .flatMap(Collection::stream)
                        .sorted(Comparator.comparing(AuditPressureDetailResp.PressureUserDetail::getNum).reversed())
                        .collect(Collectors.toList())
        );
        newAuditPressureDetailRespList.add(auditPressureDetailResp);
        return newAuditPressureDetailRespList;

    }

    @Override
    public List<AuditCapacityDetailResp> queryAC241003() {
        List<AuditCapacityDetailResp> auditCapacityDetailRespList = new ArrayList<>();
        // 获取配置参数中的操作员组信息
        List<OperatorGroupVo> groupList = getConfigOperator();
        // 获取当前在线的配置操作员列表
        List<String> onlineAcOperatorNolist = getConfigOnlineOperator(groupList);
        if (CollectionUtils.isEmpty(onlineAcOperatorNolist)) {
            log.info("[queryAC241003] 配置组的操作员，无在线操作员!");
            return auditCapacityDetailRespList;
        }
        // 查询配置操作员信息
        List<AcOperatorInfo> acOperatorInfoList = auditReportMapper.getAcOperatorInfoList(onlineAcOperatorNolist);
        Map<String, AcOperatorInfo> headOperatorInfoMap = acOperatorInfoList
                .stream()
                .collect(Collectors.toMap(AcOperatorInfo::getStaff_no, Function.identity()));
        // 当天总部坐席所有派单任务
        List<AcOperatorTodayTaskCount> todayTaskCountList = auditReportMapper.getAcOperatorTodayTaskCount(onlineAcOperatorNolist);
        // 构建任务计数映射表，便于快速查询
        Map<String, AcOperatorTodayTaskCount> taskCountMap = todayTaskCountList.stream()
                .collect(Collectors.toMap(AcOperatorTodayTaskCount::getStaff_no, Function.identity()));
        for (OperatorGroupVo operatorGroupVo : groupList) {
            // 任务数量500笔以上（当天）
            AtomicInteger taskCount500Num = new AtomicInteger();
            // 任务数量400-500笔（当天）
            AtomicInteger taskCount400Num = new AtomicInteger();
            // 任务数量300-400笔（当天）
            AtomicInteger taskCount300Num = new AtomicInteger();
            // 任务数量200-300笔（当天）
            AtomicInteger taskCount200Num = new AtomicInteger();
            // 任务数量100-200笔（当天）
            AtomicInteger taskCount100Num = new AtomicInteger();
            // 任务数量0-100笔（当天）
            AtomicInteger taskCount0Num = new AtomicInteger();
            AuditCapacityDetailResp auditCapacityDetailResp = new AuditCapacityDetailResp();
            auditCapacityDetailResp.setArea_name(operatorGroupVo.getGroup_area());
            List<String> operatorList = Arrays.asList(operatorGroupVo.getGroup_operators().split(Constant.COMMA));
            // 根据总部操作员编号集合构建产能用户详情列表
            List<AuditCapacityDetailResp.CapacityUserDetail> capacityUserDetailList = operatorList.stream().map(operatorNo -> {
                        AcOperatorTodayTaskCount taskCount = taskCountMap.get(operatorNo);
                        if (taskCount == null) {
                            return null;
                        }
                        AuditCapacityDetailResp.CapacityUserDetail capacityUserDetail = new AuditCapacityDetailResp.CapacityUserDetail();
                        capacityUserDetail.setOperator_no(operatorNo);
                        AcOperatorInfo acOperatorInfo = headOperatorInfoMap.get(operatorNo);
                        capacityUserDetail.setOperator_name(Objects.nonNull(acOperatorInfo) ? acOperatorInfo.getUser_name() : " ");
                        capacityUserDetail.setBranch_no(Objects.nonNull(acOperatorInfo) ? acOperatorInfo.getBranch_no() : " ");
                        capacityUserDetail.setBranch_name(Objects.nonNull(acOperatorInfo) ? acOperatorInfo.getBranch_name() : " ");
                        capacityUserDetail.setNum(taskCount.getTotal_num());
                        if (taskCount.getTotal_num() >= 500) {
                            taskCount500Num.getAndIncrement();
                            capacityUserDetail.setCapacity_type(CapacityTypeEnum.CAPACITY_LEVEL_6.getCode());
                        } else if (taskCount.getTotal_num() >= 400) {
                            taskCount400Num.getAndIncrement();
                            capacityUserDetail.setCapacity_type(CapacityTypeEnum.CAPACITY_LEVEL_5.getCode());
                        } else if (taskCount.getTotal_num() >= 300) {
                            taskCount300Num.getAndIncrement();
                            capacityUserDetail.setCapacity_type(CapacityTypeEnum.CAPACITY_LEVEL_4.getCode());
                        } else if (taskCount.getTotal_num() >= 200) {
                            taskCount200Num.getAndIncrement();
                            capacityUserDetail.setCapacity_type(CapacityTypeEnum.CAPACITY_LEVEL_3.getCode());
                        } else if (taskCount.getTotal_num() >= 100) {
                            taskCount100Num.getAndIncrement();
                            capacityUserDetail.setCapacity_type(CapacityTypeEnum.CAPACITY_LEVEL_2.getCode());
                        } else {
                            taskCount0Num.getAndIncrement();
                            capacityUserDetail.setCapacity_type(CapacityTypeEnum.CAPACITY_LEVEL_1.getCode());
                        }
                        return capacityUserDetail;
                    }).filter(Objects::nonNull)
                    // 按照num大小倒序
                    .sorted(Comparator.comparing(AuditCapacityDetailResp.CapacityUserDetail::getNum).reversed())
                    .collect(Collectors.toList());
            // 设置产能详情响应对象的各项数值
            auditCapacityDetailResp.setTask_count500_num(taskCount500Num.get());
            auditCapacityDetailResp.setTask_count400_num(taskCount400Num.get());
            auditCapacityDetailResp.setTask_count300_num(taskCount300Num.get());
            auditCapacityDetailResp.setTask_count200_num(taskCount200Num.get());
            auditCapacityDetailResp.setTask_count100_num(taskCount100Num.get());
            auditCapacityDetailResp.setTask_count0_num(taskCount0Num.get());
            auditCapacityDetailResp.setCapacityUserDetailList(capacityUserDetailList);
            auditCapacityDetailRespList.add(auditCapacityDetailResp);
        }
        List<AuditCapacityDetailResp> newAuditCapacityDetailRespList = new ArrayList<>(auditCapacityDetailRespList);
        // 所有数据合并为总览
        AuditCapacityDetailResp auditCapacityDetailResp = new AuditCapacityDetailResp();
        auditCapacityDetailResp.setArea_name(ALL_DATA);
        auditCapacityDetailResp.setTask_count500_num(auditCapacityDetailRespList.stream().mapToInt(AuditCapacityDetailResp::getTask_count500_num).sum());
        auditCapacityDetailResp.setTask_count400_num(auditCapacityDetailRespList.stream().mapToInt(AuditCapacityDetailResp::getTask_count400_num).sum());
        auditCapacityDetailResp.setTask_count300_num(auditCapacityDetailRespList.stream().mapToInt(AuditCapacityDetailResp::getTask_count300_num).sum());
        auditCapacityDetailResp.setTask_count200_num(auditCapacityDetailRespList.stream().mapToInt(AuditCapacityDetailResp::getTask_count200_num).sum());
        auditCapacityDetailResp.setTask_count100_num(auditCapacityDetailRespList.stream().mapToInt(AuditCapacityDetailResp::getTask_count100_num).sum());
        auditCapacityDetailResp.setTask_count0_num(auditCapacityDetailRespList.stream().mapToInt(AuditCapacityDetailResp::getTask_count0_num).sum());
        // 获取所有产能分布详情列表
        auditCapacityDetailResp.setCapacityUserDetailList(
                newAuditCapacityDetailRespList.stream()
                        .map(AuditCapacityDetailResp::getCapacityUserDetailList)
                        .flatMap(Collection::stream)
                        .sorted(Comparator.comparing(AuditCapacityDetailResp.CapacityUserDetail::getNum).reversed())
                        .collect(Collectors.toList())
        );
        newAuditCapacityDetailRespList.add(auditCapacityDetailResp);
        return newAuditCapacityDetailRespList;
    }

    @Override
    public AuditWhiteDetailResp queryAC241004() {
        AuditWhiteDetailResp auditWhiteDetailResp = new AuditWhiteDetailResp();
        // 服务绿通客户信息
        AcWhiteTaskCount acWhiteTaskCount = auditReportMapper.getAcWhiteTaskCount();
        auditWhiteDetailResp.setWhite_num(acWhiteTaskCount.getWhite_num());
        auditWhiteDetailResp.setAvg_response_time(acWhiteTaskCount.getAvg_response_time());
        // 营业部服务绿通客户信息
        List<AcBranchWhiteTaskCount> acBranchWhiteTaskCountList = auditReportMapper.getAcBranchWhiteTaskCount();
        List<AuditWhiteDetailResp.BranchWhiteDetail> branchWhiteDetailList = acBranchWhiteTaskCountList.stream().map(branchWhiteTaskCount -> {
            AuditWhiteDetailResp.BranchWhiteDetail branchWhiteDetail = new AuditWhiteDetailResp.BranchWhiteDetail();
            BeanUtils.copyProperties(branchWhiteTaskCount, branchWhiteDetail);
            return branchWhiteDetail;
        }).collect(Collectors.toList());
        auditWhiteDetailResp.setBranchWhiteDetailList(branchWhiteDetailList);
        return auditWhiteDetailResp;
    }

    @Override
    public AuditAlarmDetailResp queryAC242001() {
        AuditAlarmDetailResp auditAlarmDetailResp = new AuditAlarmDetailResp();

        // 使用 CompletableFuture 并行执行各个数据获取任务
        CompletableFuture<AuditAlarmDetailResp.PendingTaskDetail> pendingTaskFuture =
                CompletableFuture.supplyAsync(auditAlarmService::getPendingTaskDetail, monitorExecutor)
                        .exceptionally(ex -> {
                            log.error("获取待处理任务详情失败", ex);
                            return new AuditAlarmDetailResp.PendingTaskDetail();
                        });

        CompletableFuture<AuditAlarmDetailResp.AvgResponseDetail> avgResponseFuture =
                CompletableFuture.supplyAsync(auditAlarmService::getAvgResponseDetail, monitorExecutor)
                        .exceptionally(ex -> {
                            log.error("获取平均响应详情失败", ex);
                            return new AuditAlarmDetailResp.AvgResponseDetail();
                        });

        CompletableFuture<List<AuditAlarmTaskDetail>> taskWaitFuture =
                CompletableFuture.supplyAsync(auditAlarmService::getTaskWaitDetailList, monitorExecutor)
                        .exceptionally(ex -> {
                            log.error("获取任务等待详情失败", ex);
                            return new ArrayList<>();
                        });

        CompletableFuture<List<AuditAlarmTaskDetail>> failedTaskFuture =
                CompletableFuture.supplyAsync(auditAlarmService::getFailedTaskAssignmentList, monitorExecutor)
                        .exceptionally(ex -> {
                            log.error("获取失败任务分配详情失败", ex);
                            return new ArrayList<>();
                        });

        CompletableFuture<List<AuditAlarmTaskDetail>> newTaskFuture =
                CompletableFuture.supplyAsync(auditAlarmService::getNewTaskList, monitorExecutor)
                        .exceptionally(ex -> {
                            log.error("获取新增任务详情失败", ex);
                            return new ArrayList<>();
                        });

        CompletableFuture<List<AuditAlarmOperatorDetail>> operatorFuture =
                CompletableFuture.supplyAsync(auditAlarmService::getOperatorDetailList, monitorExecutor)
                        .exceptionally(ex -> {
                            log.error("获取用户操作详情失败", ex);
                            return new ArrayList<>();
                        });

        try {
            // 设置超时时间为60秒，等待所有任务完成
            CompletableFuture.allOf(pendingTaskFuture, avgResponseFuture, taskWaitFuture,
                            failedTaskFuture, newTaskFuture, operatorFuture)
                    .get(60, java.util.concurrent.TimeUnit.SECONDS);

            auditAlarmDetailResp.setPendingTaskDetail(pendingTaskFuture.get());
            auditAlarmDetailResp.setAvgResponseDetail(avgResponseFuture.get());
            auditAlarmDetailResp.setTaskWaitDetailList(taskWaitFuture.get());
            auditAlarmDetailResp.setFailedTaskAssignmentList(failedTaskFuture.get());
            auditAlarmDetailResp.setNewTaskList(newTaskFuture.get());
            auditAlarmDetailResp.setOperatorDetailList(operatorFuture.get());

        } catch (Exception e) {
            log.error("大屏告警查询详情时发生异常", e);
            throw new BizException("大屏告警查询详情失败");
        }

        return auditAlarmDetailResp;
    }


    /**
     * 构建 PressureUserDetail
     */
    private <T extends AuditPressureDetailResp.PressureUserDetail> void buildDetail(
            T detail,
            String operatorNo,
            AcOperatorInfo acOperatorInfo) {
        detail.setOperator_no(operatorNo);
        detail.setOperator_name(Objects.nonNull(acOperatorInfo) ? acOperatorInfo.getUser_name() : " ");
        detail.setBranch_no(Objects.nonNull(acOperatorInfo) ? acOperatorInfo.getBranch_no() : " ");
        detail.setBranch_name(Objects.nonNull(acOperatorInfo) ? acOperatorInfo.getBranch_name() : " ");
    }

    /**
     * 获取配置操作员
     */
    private List<OperatorGroupVo> getConfigOperator() {
        // 获取系统参数配置
        String groupConfig = compositePropertySources.getProperty(Constant.MONITOR_AC_OPERATOR_GROUP);
        if (StringUtils.isBlank(groupConfig)) {
            log.warn("获取系统参数配置={}为空", Constant.MONITOR_AC_OPERATOR_GROUP);
            return Collections.emptyList();
        }
        return JSONArray.parseArray(groupConfig).toJavaList(OperatorGroupVo.class);
    }

    /**
     * 获取配置的在线操作员
     */
    private List<String> getConfigOnlineOperator(List<OperatorGroupVo> groupList) {
        if (CollectionUtils.isEmpty(groupList)) {
            return Collections.emptyList();
        }
        // 获取在线操作员列表
        List<String> onlineUserList = baseOnlineUserDubboService.onlineUserInfo().stream().map(OnlineUserResponse::getStaff_no).collect(Collectors.toList());
        // 所有操作员，去重
        List<String> operatorNos = groupList.stream()
                .map(group -> Arrays.asList(group.getGroup_operators().split(Constant.COMMA)))
                .flatMap(List::stream)
                .distinct()
                .collect(Collectors.toList());
        log.info("获取所有配置操作员={}", JSON.toJSONString(operatorNos));
        // 获取当前在线的配置操作员列表
        return operatorNos.stream().filter(onlineUserList::contains).collect(Collectors.toList());
    }


}

