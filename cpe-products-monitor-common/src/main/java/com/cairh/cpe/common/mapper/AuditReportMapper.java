package com.cairh.cpe.common.mapper;

import com.cairh.cpe.common.dto.AuditAlarmOperatorDetail;
import com.cairh.cpe.common.dto.AuditAlarmTaskDetail;
import com.cairh.cpe.common.entity.*;

import java.util.List;

public interface AuditReportMapper {

    /**
     * 获取总部操作员信息
     */
    List<AcOperatorInfo> getAcHeadOperatorInfoList();

    /**
     * 获取操作员信息
     */
    List<AcOperatorInfo> getAcOperatorInfoList(List<String> operatorNos);

    /**
     * 获取派单任务统计-获取指定时间段内指定操作员的派单任务统计
     */
    List<AcOperatorTaskCount> getAcOperatorTaskCount(String unitTime, List<String> operatorNos);

    /**
     * 获取派单任务统计-获取指定操作员的派单见证任务统计
     */
    List<AcOperatorTodayTaskCount> getAcOperatorTodayTaskCount(List<String> operatorNos);

    /**
     * 获取绿通数据统计
     */
    AcWhiteTaskCount getAcWhiteTaskCount();

    /**
     * 获取分支绿通数据统计
     */
    List<AcBranchWhiteTaskCount> getAcBranchWhiteTaskCount();

    /**
     * 获取待处理任务数据统计
     */
    Integer getPendingTaskCount();

    /**
     * 获取平均响应时长
     */
    Integer getAvgResponseTime();

    /**
     * 获取单笔任务等待时长最长的1条客户信息
     */
    List<AuditAlarmTaskDetail> getWaitTaskTop1List();

    /**
     * 获取单笔任务分配失败次数最长的30条客户信息
     */
    List<AuditAlarmTaskDetail> getFailedAssignmentTaskTop30List();

    /**
     * 获取最新申请的30条客户信息
     */
    List<AuditAlarmTaskDetail> getNewTaskTop30List();

    /**
     * 获取空闲时间最长的30条坐席信息
     */
    List<AuditAlarmOperatorDetail> getOperatorIdleTop30List(List<String> operatorNos);

}
