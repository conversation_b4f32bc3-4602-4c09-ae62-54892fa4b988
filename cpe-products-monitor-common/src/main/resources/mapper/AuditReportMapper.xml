<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cairh.cpe.common.mapper.AuditReportMapper">

    <select id="getAcHeadOperatorInfoList" resultType="com.cairh.cpe.common.entity.AcOperatorInfo">
        select a.staff_no,
               a.user_name,
               a.branch_no,
               b.branch_name,
               b.up_branch_no
        from crh_user.operatorinfo a,
             crh_user.allbranch b
        where instr(',' || a.en_roles || ',', ',1,') > 0
          and a.branch_no = b.branch_no
          and b.branch_no = '3'
    </select>

    <select id="getAcOperatorTaskCount" resultType="com.cairh.cpe.common.entity.AcOperatorTaskCount">
        select operator_no as staff_no,
        operator_name,
        count(1) as num
        from crh_ads.dispatchtask
        where dispatch_status = '3'
        and subsys_id = '24'
        and finish_datetime > (sysdate - ${unitTime} / 1440)
        and operator_no in
        <foreach item="item" collection="operatorNos" separator="," open="(" close=")">
            #{item}
        </foreach>
        group by operator_no, operator_name
    </select>

    <select id="getAcOperatorTodayTaskCount" resultType="com.cairh.cpe.common.entity.AcOperatorTodayTaskCount">
        select
        operator_no as staff_no,
        operator_name,
        count(1) as total_num
        from crh_ads.dispatchtask
        where dispatch_status = '3'
        and subsys_id = '24'
        and finish_datetime >= trunc(sysdate)
        and finish_datetime &lt; trunc(sysdate + 1)
        and operator_no in
        <foreach item="item" collection="operatorNos" separator="," open="(" close=")">
            #{item}
        </foreach>
        group by operator_no, operator_name
    </select>

    <select id="getAcWhiteTaskCount" resultType="com.cairh.cpe.common.entity.AcWhiteTaskCount">
        select nvl(decode(task_num, 0, 0, round(response_time / task_num)), 0) as avg_response_time,
               white_num
        from (select nvl(sum(decode(a.task_status, '3', 1, '4', 1, '8', 1, 0)), 0) as task_num,
                     count(distinct request_no)                                    as white_num,
                     round(nvl(sum(decode(a.task_status, '3', (a.deal_datetime - a.white_datetime), '4',
                                          (a.deal_datetime - a.white_datetime), '8',
                                          (a.deal_datetime - a.white_datetime),
                                          0)), 0) * 24 * 60 * 60)                  as response_time
              from crh_ac.flowtaskrecorddetails a
              where a.white_flag = '1'
                and a.white_datetime >= trunc(sysdate)
                and a.white_datetime &lt; trunc(sysdate + 1)
                and a.invalid_flag = '0')
    </select>

    <select id="getAcBranchWhiteTaskCount" resultType="com.cairh.cpe.common.entity.AcBranchWhiteTaskCount">
        select branch_white_num,
               branch_handle_num,
               branch_all_num,
               branch_no,
               branch_name,
               nvl(decode(branch_handle_num, 0, 0, round(branch_response_time / branch_all_num)),
                   0) as branch_avg_response_time
        from (select count(distinct a.request_no)                                  as branch_white_num,
                     count(distinct case
                                        when a.task_status in ('3', '4', '8')
                                            then a.request_no end)                 as branch_handle_num,
                     nvl(sum(decode(a.task_status, '3', 1, '4', 1, '8', 1, 0)), 0) as branch_all_num,
                     a.branch_no,
                     a.branch_name,
                     round(nvl(sum(decode(a.task_status, '3', (a.deal_datetime - a.white_datetime), '4',
                                          (a.deal_datetime - a.white_datetime), '8',
                                          (a.deal_datetime - a.white_datetime),
                                          0)), 0) * 24 * 60 *
                           60)                                                     as branch_response_time
              from crh_ac.flowtaskrecorddetails a
              where a.white_flag = '1'
                and a.white_datetime >= trunc(sysdate)
                and a.white_datetime &lt; trunc(sysdate + 1)
                and a.invalid_flag = '0'
                and a.branch_no != ' '
              group by a.branch_no, a.branch_name)
    </select>

    <select id="getPendingTaskCount" resultType="java.lang.Integer">
        select count(*)
        from crh_ac.flowtaskrecorddetails a
        where a.task_status = '1'
    </select>

    <select id="getAvgResponseTime" resultType="java.lang.Integer">
        select nvl(decode(task_num, 0, 0, round(response_time / task_num)), 0) as avg_response_time
        from (select nvl(sum(decode(a.task_status, '3', 1, '4', 1, '8', 1, 0)), 0) as task_num,
                     round(nvl(sum(decode(a.task_status, '3', (a.first_deal_datetime - a.request_datetime), '4',
                                          (a.first_deal_datetime - a.request_datetime), '8',
                                          (a.first_deal_datetime - a.request_datetime),
                                          0)), 0) * 24 * 60 * 60)                  as response_time
              from crh_ac.flowtaskrecorddetails a
              where a.request_datetime >= trunc(sysdate)
                and a.request_datetime &lt; trunc(sysdate + 1)
                and a.invalid_flag = '0')
    </select>

    <select id="getWaitTaskTop30List" resultType="com.cairh.cpe.common.dto.AuditAlarmTaskDetail">
        select *
        from (select b.user_name as client_name,
                     b.id_no,
                     b.request_datetime
              from crh_ac.businprocessrequestaudittrail b
              where b.request_status in
                    ('audit-1', 'review-1', 'secondary_review-1')
              order by b.request_datetime)
        where rownum &lt;= 30
    </select>

    <select id="getFailedAssignmentTaskTop30List" resultType="com.cairh.cpe.common.dto.AuditAlarmTaskDetail">
        select *
        from (select d.client_name,
                     d.id_no,
                     d.dispatch_fail_count as fail_assignment_num
              from crh_ads.dispatchtask d
              where d.dispatch_status in ('0', '6')
              order by d.dispatch_fail_count desc)
        where rownum &lt;= 30
    </select>

    <select id="getNewTaskTop30List" resultType="com.cairh.cpe.common.dto.AuditAlarmTaskDetail">
        select *
        from (select b.user_name as client_name,
                     b.id_no,
                     b.request_datetime
              from crh_ac.businprocessrequestaudittrail b
              where b.request_datetime >= trunc(sysdate)
                and b.request_datetime &lt; trunc(sysdate + 1)
              order by b.request_datetime desc)
        where rownum &lt;= 30
    </select>

    <select id="getOperatorIdleTop30List" resultType="com.cairh.cpe.common.dto.AuditAlarmOperatorDetail">
        select *
        from (
        select f.operator_no,
        max(f.operator_name) as operator_name,
        max(f.deal_datetime) as deal_datetime
        from crh_ac.flowtaskrecorddetails f
        where f.task_status in ('3', '4', '8')
        and f.deal_datetime >= trunc(sysdate)
        and f.deal_datetime &lt; trunc(sysdate + 1)
        and f.operator_no in
        <foreach item="item" collection="operatorNos" separator="," open="(" close=")">
            #{item}
        </foreach>
        group by f.operator_no
        order by max(f.deal_datetime)
        )
        where rownum &lt;= 30
    </select>

</mapper>
