package com.cairh.cpe.monitor.worker.data.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.alibaba.nacos.common.utils.StringUtils;
import com.cairh.cpe.common.constant.CacheKeyConfig;
import com.cairh.cpe.common.constant.Constant;
import com.cairh.cpe.common.dto.AuditAlarmDetailResp;
import com.cairh.cpe.common.dto.AuditAlarmTaskDetail;
import com.cairh.cpe.common.service.IAuditAlarmService;
import com.cairh.cpe.common.util.ComponentWorkTimeService;
import com.cairh.cpe.core.autoconfiure.env.CompositePropertySources;
import com.cairh.cpe.esb.component.notice.IEsbComponentSmsDubboService;
import com.cairh.cpe.esb.component.notice.dto.req.NoticeSendSmsRequest;
import com.cairh.cpe.monitor.worker.constant.AuditAlarmSmsNoticeMessageEnum;
import com.cairh.cpe.monitor.worker.data.service.IAuditAlarmSmsNoticeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/7/1 09:50
 */
@Slf4j
@Service
public class AuditAlarmSmsNoticeServiceImpl implements IAuditAlarmSmsNoticeService {

    @Autowired
    protected CompositePropertySources compositePropertySources;
    @Autowired
    private IAuditAlarmService auditAlarmService;
    @DubboReference(check = false, lazy = true)
    private IEsbComponentSmsDubboService smsDubboService;
    @Autowired
    private StringRedisTemplate redisTemplate;
    @Autowired
    private ComponentWorkTimeService componentWorkTimeService;

    @Override
    public void pendingTaskAlarmNotice() {
        AuditAlarmDetailResp.PendingTaskDetail pendingTaskDetail = auditAlarmService.getPendingTaskDetail();
        if (pendingTaskDetail == null) {
            return;
        }

        String type = pendingTaskDetail.getType();
        // 告警
        if (StringUtils.equals(Constant.ONE_STR, type)) {
            if (!alarmTime()) {
                log.info("非告警通知时间，不发送短信");
                return;
            }
            // 待处理任务告警缓存key
            String redisKey = CacheKeyConfig.AUDIT_ALARM_REDIS_KEY + AuditAlarmSmsNoticeMessageEnum.PENDINGTASK.getCode();
            if (redisKeyExists(redisKey)) {
                log.info("待处理任务告警短信通知已发送，不重复发送");
                return;
            }
            // 填充短信消息内容。间隔
            String message = String.format(AuditAlarmSmsNoticeMessageEnum.PENDINGTASK.getValue(), pendingTaskDetail.getPending_task_num());
            sendMessage(message);
        }
    }

    @Override
    public void avgResponseAlarmNotice() {
        AuditAlarmDetailResp.AvgResponseDetail avgResponseDetail = auditAlarmService.getAvgResponseDetail();
        if (avgResponseDetail == null) {
            return;
        }

        String type = avgResponseDetail.getType();
        // 告警
        if (StringUtils.equals(Constant.ONE_STR, type)) {
            if (!alarmTime()) {
                log.info("非告警通知时间，不发送短信");
                return;
            }
            // 平均响应时间告警缓存key
            String redisKey = CacheKeyConfig.AUDIT_ALARM_REDIS_KEY + AuditAlarmSmsNoticeMessageEnum.AVGRESPONSE.getCode();
            if (redisKeyExists(redisKey)) {
                log.info("平均响应时间告警短信通知已发送，不重复发送");
                return;
            }
            // 填充短信消息内容。间隔
            String message = String.format(AuditAlarmSmsNoticeMessageEnum.AVGRESPONSE.getValue(), avgResponseDetail.getAvg_response_time());
            sendMessage(message);
        }
    }

    @Override
    public void newTaskAlarmNotice() {
        List<AuditAlarmTaskDetail> newTaskList = auditAlarmService.getNewTaskList();
        if (CollectionUtils.isEmpty(newTaskList)) {
            return;
        }
        AuditAlarmTaskDetail newTaskDetail = newTaskList.get(0);
        if (newTaskDetail == null) {
            return;
        }

        String type = newTaskDetail.getType();
        // 告警
        if (StringUtils.equals(Constant.ONE_STR, type)) {
            if (!alarmTime()) {
                log.info("非告警通知时间，不发送短信");
                return;
            }
            // 新进任务告警缓存key
            String redisKey = CacheKeyConfig.AUDIT_ALARM_REDIS_KEY + AuditAlarmSmsNoticeMessageEnum.NEWTASK.getCode();
            if (redisKeyExists(redisKey)) {
                log.info("新进任务告警短信通知已发送，不重复发送");
                return;
            }
            // 填充短信消息内容。间隔
            String message = String.format(AuditAlarmSmsNoticeMessageEnum.NEWTASK.getValue(), newTaskDetail.getInterval_time());
            sendMessage(message);
        }
    }

    /**
     * 当前时间是否为告警短信通知时间
     */
    private boolean alarmTime() {
        // 判断是否是告警通知时间
        return componentWorkTimeService.isWorkTime(Constant.WORK_TIME_AUDIT_ALARM);
    }

    /**
     * redis是否存在该key
     * 并处理
     */
    private boolean redisKeyExists(String redisKey) {
        // redis是否存在该key
        if (redisTemplate.hasKey(redisKey)) {
            return true;
        }
        // 通知间隔时间：分钟，默认60
        int intervalSeconds = Integer.parseInt(compositePropertySources.getProperty(Constant.MONITOR_ALARM_SMS_INTERVAL_TIME, "60"));
        // 设置redis缓存时间为通知间隔时间
        redisTemplate.opsForValue().set(redisKey, Constant.ONE_STR, intervalSeconds);
        return false;
    }

    /**
     * 发送短信
     */
    private void sendMessage(String message) {
        // 获取手机号
        String mobile_tel = compositePropertySources.getProperty(Constant.MONITOR_ALARM_SMS_NOTICE_MOBILE);
        if (StringUtils.isEmpty(mobile_tel)) {
            log.warn("mobile_tel is empty!");
            return;
        }
        // 手机号多个用逗号隔开，需要多次发送
        String[] mobile_tel_array = mobile_tel.split(Constant.COMMA);
        for (String mobile_tel_item : mobile_tel_array) {
            if (StringUtils.isEmpty(mobile_tel_item)) {
                continue;
            }
            // 短信发送
            NoticeSendSmsRequest noticeSendSmsRequest = new NoticeSendSmsRequest();
            noticeSendSmsRequest.setMsg_content(message);
            noticeSendSmsRequest.setSend_type(Constant.SEND_TYPE);
            noticeSendSmsRequest.setMobile_tel(mobile_tel_item);
            noticeSendSmsRequest.setChannel_type(Constant.CHANNEL_TYPE);
            noticeSendSmsRequest.setService_vender(Constant.KAFKA_SERVICE_VENDER);
            log.info("监控告警短信信息：{}", JSON.toJSONString(noticeSendSmsRequest));
            smsDubboService.noticeSendSms(noticeSendSmsRequest);
        }
    }
}
